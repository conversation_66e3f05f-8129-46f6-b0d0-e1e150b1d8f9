'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { getComandaColorClasses } from '@/utils/softColors'
import { Progress } from '@/components/ui/progress'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useAuth } from '@/contexts/AuthContext'
import { comandeApi, responsabiliApi } from '@/lib/api'
import { Comanda, Responsabile } from '@/types'
import CreaComandaDialog from '@/components/comande/CreaComandaDialog'
import GestisciResponsabiliDialog from '@/components/comande/GestisciResponsabiliDialog'
import DettagliComandaDialog from '@/components/comande/DettagliComandaDialog'
import { useToast } from '@/hooks/use-toast'
import {
  ClipboardList,
  Plus,
  Users,
  Clock,
  CheckCircle,
  AlertTriangle,
  Play,
  Pause,
  Square,
  Eye,
  Edit,
  Trash2,
  Loader2,
  Search,
  Filter,
  Settings
} from 'lucide-react'

export default function ComandePage() {
  const [selectedTab, setSelectedTab] = useState('active')
  const [comande, setComande] = useState<Comanda[]>([])
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedResponsabile, setSelectedResponsabile] = useState('all')
  const [selectedTipo, setSelectedTipo] = useState('all')
  const [showCreaComandaDialog, setShowCreaComandaDialog] = useState(false)
  const [showResponsabiliDialog, setShowResponsabiliDialog] = useState(false)
  const [showDettagliDialog, setShowDettagliDialog] = useState(false)
  const [selectedComandaCode, setSelectedComandaCode] = useState<string | null>(null)

  const { user, cantiere } = useAuth()
  const { toast } = useToast()

  // Get cantiere ID con fallback al localStorage come nelle altre pagine
  const [cantiereId, setCantiereId] = useState<number>(0)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')
      setCantiereId(storedId)
      console.log('🏗️ ComandePage - CantiereId aggiornato:', {
        cantiereFromAuth: cantiere?.id_cantiere,
        storedId: storedId,
        localStorage: localStorage.getItem('selectedCantiereId'),
        finalCantiereId: storedId
      })

      // Debug aggiuntivo
      if (storedId === 0) {
        console.warn('⚠️ PROBLEMA: CantiereId è 0! Verifica selezione cantiere')
        console.log('🔍 Debug localStorage:', {
          selectedCantiereId: localStorage.getItem('selectedCantiereId'),
          selectedCantiereName: localStorage.getItem('selectedCantiereName'),
          allLocalStorageKeys: Object.keys(localStorage)
        })
      }
    }
  }, [cantiere])

  // Carica comande e responsabili dal backend
  useEffect(() => {
    console.log('🔄 useEffect loadData triggered:', { cantiereId, isValid: cantiereId > 0 })
    if (cantiereId && cantiereId > 0) {
      console.log('✅ Caricamento dati per cantiere:', cantiereId)
      loadData()
    } else {
      console.warn('❌ CantiereId non valido, non carico dati:', cantiereId)
    }
  }, [cantiereId])

  const loadData = async () => {
    try {
      setIsLoading(true)
      setError('')

      if (!cantiereId || cantiereId <= 0) {
        setError('Cantiere non selezionato')
        return
      }

      console.log('🔄 Caricamento dati comande per cantiere:', cantiereId)
      console.log('📡 Chiamata API comande URL:', `/api/comande/cantiere/${cantiereId}`)
      console.log('📡 Chiamata API responsabili URL:', `/api/responsabili/cantiere/${cantiereId}`)

      const [comandeResponse, responsabiliData] = await Promise.all([
        comandeApi.getComande(cantiereId),
        responsabiliApi.getResponsabili(cantiereId)
      ])

      console.log('🔍 Comande response received:', comandeResponse)
      console.log('🔍 Responsabili data received:', responsabiliData)

      // Extract comande array from the response object
      const comandeData = comandeResponse?.data?.comande || comandeResponse?.comande || comandeResponse?.data || comandeResponse || []
      console.log('🔍 Extracted comande data:', comandeData)
      console.log('🔍 Comande is array:', Array.isArray(comandeData))

      // Extract responsabili array from the response object
      const responsabiliArray = responsabiliData?.data || responsabiliData || []

      // Ensure we always set arrays
      setComande(Array.isArray(comandeData) ? comandeData : [])
      setResponsabili(Array.isArray(responsabiliArray) ? responsabiliArray : [])
    } catch (error: any) {
      console.error('Errore caricamento dati:', error)
      setError(error.response?.data?.detail || 'Errore durante il caricamento dei dati')
    } finally {
      setIsLoading(false)
    }
  }

  // Funzioni per gestire i dialog
  const handleSuccess = (message: string) => {
    toast({
      title: "Successo",
      description: message,
    })
    loadData() // Ricarica i dati
  }

  const handleError = (message: string) => {
    toast({
      title: "Errore",
      description: message,
      variant: "destructive",
    })
  }

  const handleDeleteComanda = async (codiceComanda: string) => {
    if (!confirm(`Sei sicuro di voler eliminare la comanda ${codiceComanda}?`)) {
      return
    }

    try {
      setIsLoading(true)
      await comandeApi.deleteComanda(cantiereId, codiceComanda)
      handleSuccess(`Comanda ${codiceComanda} eliminata con successo`)
    } catch (error: any) {
      console.error('Errore eliminazione comanda:', error)
      handleError('Errore durante l\'eliminazione della comanda')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCambiaStato = async (codiceComanda: string, nuovoStato: string) => {
    try {
      setIsLoading(true)
      await comandeApi.cambiaStato(cantiereId, codiceComanda, nuovoStato)
      handleSuccess(`Stato della comanda ${codiceComanda} cambiato in ${nuovoStato}`)
    } catch (error: any) {
      console.error('Errore cambio stato:', error)
      handleError('Errore durante il cambio di stato')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (stato: string) => {
    switch (stato) {
      case 'COMPLETATA':
        return <Badge className="bg-green-100 text-green-800">Completata</Badge>
      case 'IN_CORSO':
        return <Badge className="bg-blue-100 text-blue-800">In Corso</Badge>
      case 'ASSEGNATA':
        return <Badge className="bg-yellow-100 text-yellow-800">Assegnata</Badge>
      case 'CREATA':
        return <Badge className="bg-gray-100 text-gray-800">Creata</Badge>
      case 'ANNULLATA':
        return <Badge className="bg-red-100 text-red-800">Annullata</Badge>
      default:
        return <Badge variant="secondary">{stato}</Badge>
    }
  }

  const getTipoBadge = (tipo: string) => {
    const colorClasses = getComandaColorClasses(tipo)

    const tipoLabels: { [key: string]: string } = {
      'POSA': '🔧 Posa',
      'COLLEGAMENTO_PARTENZA': '🔌 Coll. Partenza',
      'COLLEGAMENTO_ARRIVO': '⚡ Coll. Arrivo',
      'CERTIFICAZIONE': '📋 Certificazione'
    }

    return (
      <Badge className={colorClasses.badge}>
        {tipoLabels[tipo] || tipo.replace(/_/g, ' ')}
      </Badge>
    )
  }

  const filteredComande = Array.isArray(comande) ? comande.filter(comanda => {
    // Filtro per tab
    let passesTabFilter = true
    switch (selectedTab) {
      case 'active':
        passesTabFilter = comanda.stato === 'IN_CORSO' || comanda.stato === 'ASSEGNATA' || comanda.stato === 'CREATA'
        break
      case 'completed':
        passesTabFilter = comanda.stato === 'COMPLETATA'
        break
      case 'all':
        passesTabFilter = true
        break
      default:
        passesTabFilter = true
    }

    // Filtro per ricerca
    const passesSearchFilter = searchTerm === '' ||
      comanda.codice_comanda.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (comanda.descrizione && comanda.descrizione.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (comanda.responsabile && comanda.responsabile.toLowerCase().includes(searchTerm.toLowerCase()))

    // Filtro per responsabile
    const passesResponsabileFilter = selectedResponsabile === 'all' || comanda.responsabile === selectedResponsabile

    // Filtro per tipo
    const passesTipoFilter = selectedTipo === 'all' || comanda.tipo_comanda === selectedTipo

    return passesTabFilter && passesSearchFilter && passesResponsabileFilter && passesTipoFilter
  }) : []

  const stats = {
    totali: Array.isArray(comande) ? comande.length : 0,
    in_corso: Array.isArray(comande) ? comande.filter(c => c.stato === 'IN_CORSO').length : 0,
    completate: Array.isArray(comande) ? comande.filter(c => c.stato === 'COMPLETATA').length : 0,
    pianificate: Array.isArray(comande) ? comande.filter(c => c.stato === 'CREATA' || c.stato === 'ASSEGNATA').length : 0,
    filtrate: filteredComande.length
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[90%] mx-auto space-y-6">

        {/* Header con titolo e azioni */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-slate-900">Gestione Comande</h1>
            <p className="text-slate-600">
              {cantiereId > 0 ? `Cantiere ${localStorage.getItem('selectedCantiereName') || cantiereId}` : 'Nessun cantiere selezionato'}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowResponsabiliDialog(true)}
            >
              <Settings className="h-4 w-4 mr-2" />
              Gestisci Responsabili
            </Button>
            <Button
              size="sm"
              onClick={() => setShowCreaComandaDialog(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Nuova Comanda
            </Button>
          </div>
        </div>

        {/* Filtri e ricerca */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex-1 min-w-[200px]">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="Cerca per codice, descrizione o responsabile..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <select
                value={selectedTipo}
                onChange={(e) => setSelectedTipo(e.target.value)}
                className="px-3 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Tutti i tipi</option>
                <option value="POSA">🔧 Posa</option>
                <option value="COLLEGAMENTO_PARTENZA">🔌 Coll. Partenza</option>
                <option value="COLLEGAMENTO_ARRIVO">⚡ Coll. Arrivo</option>
                <option value="CERTIFICAZIONE">📋 Certificazione</option>
              </select>

              <select
                value={selectedResponsabile}
                onChange={(e) => setSelectedResponsabile(e.target.value)}
                className="px-3 py-2 border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Tutti i responsabili</option>
                {responsabili.map((resp) => (
                  <option key={resp.id_responsabile} value={resp.nome_responsabile}>
                    {resp.nome_responsabile}
                  </option>
                ))}
              </select>

              {(searchTerm || selectedTipo !== 'all' || selectedResponsabile !== 'all') && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedTipo('all')
                    setSelectedResponsabile('all')
                  }}
                >
                  Pulisci filtri
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Totali</p>
                  <p className="text-2xl font-bold text-slate-900">{stats.totali}</p>
                </div>
                <ClipboardList className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">In Corso</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.in_corso}</p>
                </div>
                <Play className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Completate</p>
                  <p className="text-2xl font-bold text-green-600">{stats.completate}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-slate-600">Pianificate</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.pianificate}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          {/* Card per risultati filtrati */}
          {(searchTerm || selectedTipo !== 'all' || selectedResponsabile !== 'all') && (
            <Card className="border-2 border-blue-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-blue-600">Filtrate</p>
                    <p className="text-2xl font-bold text-blue-700">{stats.filtrate}</p>
                  </div>
                  <Filter className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Comande List */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Elenco Comande</CardTitle>
                    <CardDescription>
                      {filteredComande.length === stats.totali
                        ? `${stats.totali} comande totali`
                        : `${filteredComande.length} di ${stats.totali} comande`
                      }
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    {[
                      { key: 'active', label: 'Attive', count: stats.in_corso + stats.pianificate },
                      { key: 'completed', label: 'Completate', count: stats.completate },
                      { key: 'all', label: 'Tutte', count: stats.totali }
                    ].map((tab) => (
                      <Button
                        key={tab.key}
                        variant={selectedTab === tab.key ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setSelectedTab(tab.key)}
                        className="relative"
                      >
                        {tab.label}
                        {tab.count > 0 && (
                          <Badge
                            variant="secondary"
                            className="ml-2 text-xs px-1.5 py-0.5 min-w-[20px] h-5"
                          >
                            {tab.count}
                          </Badge>
                        )}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Caricamento comande...
                    </div>
                  </div>
                ) : error ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="flex items-center gap-2 text-red-600">
                      <AlertTriangle className="h-4 w-4" />
                      {error}
                    </div>
                  </div>
                ) : filteredComande.length === 0 ? (
                  <div className="text-center py-8 text-slate-500">
                    Nessuna comanda trovata
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredComande.map((comanda) => (
                      <Card key={comanda.codice_comanda} className="border-l-4 border-l-blue-500">
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div>
                              <h4 className="font-semibold text-slate-900">{comanda.codice_comanda}</h4>
                              <p className="text-sm text-slate-600">{comanda.descrizione || 'Nessuna descrizione'}</p>
                            </div>
                            <div className="flex gap-2">
                              {getTipoBadge(comanda.tipo_comanda)}
                              {getStatusBadge(comanda.stato)}
                            </div>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3 text-sm">
                            <div>
                              <p className="text-slate-500">Responsabile</p>
                              <p className="font-medium">{comanda.responsabile || 'Non assegnato'}</p>
                            </div>
                            <div>
                              <p className="text-slate-500">Team</p>
                              <p className="font-medium">{comanda.numero_componenti_squadra || 0} persone</p>
                            </div>
                            <div>
                              <p className="text-slate-500">Creazione</p>
                              <p className="font-medium">{new Date(comanda.data_creazione).toLocaleDateString('it-IT')}</p>
                            </div>
                            <div>
                              <p className="text-slate-500">Scadenza</p>
                              <p className="font-medium">
                                {comanda.data_scadenza ? new Date(comanda.data_scadenza).toLocaleDateString('it-IT') : 'Non definita'}
                              </p>
                            </div>
                          </div>

                          {comanda.data_completamento && (
                            <div className="mb-3 p-2 bg-green-50 rounded-lg">
                              <p className="text-sm text-green-700">
                                Completata il {new Date(comanda.data_completamento).toLocaleDateString('it-IT')}
                              </p>
                            </div>
                          )}

                          <div className="flex gap-2 flex-wrap">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedComandaCode(comanda.codice_comanda)
                                setShowDettagliDialog(true)
                              }}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              Dettagli
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                // TODO: Implementare modifica comanda
                                toast({
                                  title: "Funzione in sviluppo",
                                  description: "La modifica comande sarà disponibile presto",
                                })
                              }}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Modifica
                            </Button>
                            {comanda.stato === 'IN_CORSO' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCambiaStato(comanda.codice_comanda, 'SOSPESA')}
                                disabled={isLoading}
                              >
                                <Pause className="h-4 w-4 mr-1" />
                                Sospendi
                              </Button>
                            )}
                            {(comanda.stato === 'CREATA' || comanda.stato === 'ASSEGNATA') && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCambiaStato(comanda.codice_comanda, 'IN_CORSO')}
                                disabled={isLoading}
                              >
                                <Play className="h-4 w-4 mr-1" />
                                Avvia
                              </Button>
                            )}
                            {comanda.stato === 'SOSPESA' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCambiaStato(comanda.codice_comanda, 'IN_CORSO')}
                                disabled={isLoading}
                              >
                                <Play className="h-4 w-4 mr-1" />
                                Riprendi
                              </Button>
                            )}
                            {(comanda.stato === 'IN_CORSO' || comanda.stato === 'SOSPESA') && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleCambiaStato(comanda.codice_comanda, 'COMPLETATA')}
                                disabled={isLoading}
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Completa
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteComanda(comanda.codice_comanda)}
                              disabled={isLoading}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4 mr-1" />
                              Elimina
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Responsabili Sidebar */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Responsabili
                </CardTitle>
                <CardDescription>Gestione responsabili di cantiere</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Caricamento...
                  </div>
                ) : (
                  <>
                    <div className="space-y-4">
                      {responsabili.map((responsabile) => {
                        const comandeAttive = Array.isArray(comande) ? comande.filter(c =>
                          c.responsabile === responsabile.nome_responsabile &&
                          (c.stato === 'IN_CORSO' || c.stato === 'ASSEGNATA')
                        ).length : 0

                        return (
                          <div key={responsabile.id_responsabile} className="p-3 bg-slate-50 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium text-slate-900">{responsabile.nome_responsabile}</h4>
                              <Badge variant={comandeAttive > 0 ? 'default' : 'secondary'}>
                                {comandeAttive} attive
                              </Badge>
                            </div>
                            <div className="text-sm text-slate-600 space-y-1">
                              {responsabile.numero_telefono && <p>{responsabile.numero_telefono}</p>}
                              {responsabile.mail && <p>{responsabile.mail}</p>}
                            </div>
                          </div>
                        )
                      })}
                    </div>

                    <Button variant="outline" size="sm" className="w-full mt-4">
                      <Plus className="h-4 w-4 mr-2" />
                      Nuovo Responsabile
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

      </div>

      {/* Dialog per creare comanda */}
      <CreaComandaDialog
        open={showCreaComandaDialog}
        onClose={() => setShowCreaComandaDialog(false)}
        onSuccess={handleSuccess}
        onError={handleError}
        onComandaCreated={() => loadData()}
      />

      {/* Dialog per gestire responsabili */}
      <GestisciResponsabiliDialog
        open={showResponsabiliDialog}
        onClose={() => setShowResponsabiliDialog(false)}
        onSuccess={handleSuccess}
        onError={handleError}
      />

      {/* Dialog per dettagli comanda */}
      <DettagliComandaDialog
        open={showDettagliDialog}
        onClose={() => {
          setShowDettagliDialog(false)
          setSelectedComandaCode(null)
        }}
        codiceComanda={selectedComandaCode}
        onSuccess={handleSuccess}
        onError={handleError}
      />
    </div>
  )
}
