{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "2b242b0fb7157d5f154575b7ab949103", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "25b2af4b1871a4b02ebfb9f4397f4853988b2d9a14d27a954de291bc0fdbe4b6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3b5b62eb405b9daf9fc8d0271c36dd1a82315027b68c9cb186cf0e1e461ae6cb"}}}, "sortedMiddleware": ["/"], "functions": {}}