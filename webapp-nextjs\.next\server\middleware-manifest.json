{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "1b4e02140369946f15b951dd2453ac5e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "316f53771e793c603819ced8ce72311864d713fbaadf6b3382bc9a78a3d65993", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7efb10f372354195f64a50cdeb7fb60555c2b26fc7a140525153354512335ef7"}}}, "sortedMiddleware": ["/"], "functions": {}}